if (!window.rc_rooms) {
  class Room {
    constructor(roomId, rawRoom, seren) {
      this.id = roomId;
      this.raw = rawRoom;
      this.seren = seren;
      this.members = {};
      this.messages = [];
      this.events = [];
      this.lastMessage = null;
      this.isActive = false;
      this.flaggedStatus = null;
      this._processEvents();
    }

    _processEvents() {
      const stateEvents = this.raw.state?.events || [];
      const timelineEvents = this.raw.timeline?.events || [];

      stateEvents.forEach(event => {
        if (event.type === 'm.room.member') {
          const userId = event.state_key;
          this.members[userId] = {
            userId,
            userName: event.content.displayname || event.content.name || userId,
            accountName: userId.split(':')[0].replace(/^@/, '')
          };
        }
      });

      timelineEvents.forEach(event => {
        if (event.type === 'm.room.message' && !event.unsigned?.redacted_because) {
          const message = this._createMessage(event);
          if (message) {
            this.messages.push(message);
          }
        }
        if ([
          'com.reddit.invite_warning_label',
          'com.reddit.invite_spam_status',
          'com.reddit.spam_status',
          'com.reddit.shadowban_status',
          'com.reddit.user_suspension_status'
        ].includes(event.type)) {
          this.flaggedStatus = {
            type: event.type,
            sender: this.members[event.sender]?.userName || event.sender,
            status: event.content.status,
            time: new Date(event.origin_server_ts).toISOString(),
          };
          this.events.push(this.flaggedStatus);
        }
      });

      this.messages.sort((a, b) => new Date(a.time) - new Date(b.time));
      this.lastMessage = this.messages[this.messages.length - 1] || null;
    }

    _createMessage(event) {
      if (!event.content) return null;
      const { body, "com.reddit.blurred_url": blurredUrl } = event.content;
      const text_message = body || "";
      const content = blurredUrl
        ? { type: 'media', text: { value: '' }, media: { url: blurredUrl, base64_data: "" } }
        : { type: 'text', text: { value: text_message }, media: { url: '', base64_data: '' } };

      return {
        id: event.event_id,
        message_id: event.event_id,
        message_time: new Date(event.origin_server_ts).toISOString(),
        room_id: this.id,
        account_id: this.seren.name,
        time: new Date(event.origin_server_ts).toISOString(),
        message_text: blurredUrl ? "" : text_message,
        user_id: event.sender,
        user_name: this.members[event.sender]?.userName || event.sender,
        content: content
      };
    }

    getOtherMember() {
      const otherMember = Object.values(this.members).find(m => m.userId !== this.seren.account_id);
      return otherMember || null;
    }

    checkActivity(threshold) {
      if (!this.lastMessage) return false;
      const now = Date.now();
      const lastMsgTime = new Date(this.lastMessage.time).getTime();
      this.isActive = (now - lastMsgTime) < threshold;
      return this.isActive;
    }
  }

  class RoomCache {
    constructor(RoomsQuery) {
      this.db = RoomsQuery;
      this.rooms = new Map();
      this.requests = new Map();
      this.seren = { account_id: null, name: 'seren' };
      this.isInitialized = false;
      this.last_sync_ts = 0;
    }

    async initialize() {
      if (this.isInitialized) return;
      await this.db._withTransaction(['accountData', 'sync'], 'readonly', async (tx) => {
        this.seren.account_id = await this.db.execute_cursor(
          tx.objectStore('accountData'),
          (cursor) => {
            const item = cursor.value;
            if (item.type === 'm.push_rules') {
              const pattern = item.content.global.content.find(r => r.rule_id === '.m.rule.contains_user_name').pattern;
              return `@${pattern}:reddit.com`;
            }
            return false;
          }
        );

        await this.db.execute_cursor(tx.objectStore('sync'), (cursor) => {
          const roomsData = cursor.value.roomsData.join;
          if (roomsData) {
            Object.entries(roomsData).forEach(([roomId, roomInfo]) => {
              const room = new Room(roomId, roomInfo, this.seren);
              this.rooms.set(roomId, room);
            });
          }
          const peekData = cursor.value.roomsData.peek;
          if (peekData) {
            Object.entries(peekData).forEach(([roomId, roomInfo]) => {
              const room = new Room(roomId, roomInfo, this.seren);
              this.requests.set(roomId, room);
            });
          }
        });
      });
      this.isInitialized = true;
    }

    getRoom(roomId) {
      return this.rooms.get(roomId);
    }

    _filterUpdatedRooms(since) {
      return Array.from(this.rooms.values()).filter(room => {
        if (!room.raw.timeline?.events?.length) return false;
        const lastEvent = room.raw.timeline.events[room.raw.timeline.events.length - 1];
        return lastEvent.origin_server_ts > since;
      });
    }

    getActiveRooms(threshold = 15 * 60 * 1000) {
      const since = Date.now() - threshold;
      const activeRooms = [];
      const roomsToCheck = since > 0 ? this._filterUpdatedRooms(since) : this.rooms.values();
      
      for (const room of roomsToCheck) {
        if (room.checkActivity(threshold)) {
          activeRooms.push({
            roomId: room.id,
            sender_name: room.getOtherMember()?.userName || 'Unknown',
            sender: room.lastMessage.user_id,
            time: room.lastMessage.time,
            messageId: room.lastMessage.id
          });
        }
      }
      return activeRooms;
    }

    getFlaggedRooms() {
      const flaggedRooms = {};
      for (const room of this.rooms.values()) {
        if (room.flaggedStatus) {
          flaggedRooms[room.id] = {
            ...room.flaggedStatus,
            members: room.members
          };
        }
      }
      return flaggedRooms;
    }

    async _get_sync() {
      return this.db._withTransaction(['sync'], 'readonly', async (tx) => {
        const store = tx.objectStore('sync');
        return new Promise((resolve) => {
          const req = store.openCursor();
          req.onsuccess = () => resolve(req.result?.value);
          req.onerror = () => resolve(null);
        });
      });
    }

    async updateCache(force = false) {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const syncStartTime = Date.now();
      const updatedRooms = [];
      const syncData = await this._get_sync();

      if (!syncData?.roomsData?.join) {
        return { 
          updatedAt: new Date().toISOString(), 
          updatedRooms, 
          totalRooms: this.rooms.size,
          lastSync: this.last_sync_ts,
          syncDuration: Date.now() - syncStartTime
        };
      }

      for (const [roomId, roomData] of Object.entries(syncData.roomsData.join)) {
        const existingRoom = this.rooms.get(roomId);
        const roomLastUpdated = roomData.timeline?.events[roomData.timeline.events.length - 1]?.origin_server_ts || 0;
        
        if (force || !existingRoom || 
            !existingRoom.raw.timeline?.events?.length ||
            roomLastUpdated > existingRoom.raw.timeline.events[existingRoom.raw.timeline.events.length - 1]?.origin_server_ts) {
          
          const updatedRoom = new Room(roomId, roomData, this.seren);
          this.rooms.set(roomId, updatedRoom);
          updatedRooms.push(roomId);
        }
      }
      
      this.last_sync_ts = syncStartTime;

      return {
        updatedAt: new Date().toISOString(),
        updatedRooms,
        totalRooms: this.rooms.size
      };
    }
  }

  class RoomsQuery {
    constructor() {
      this.db_name = 'matrix-js-sdk:reddit-chat-sync';
      this.db = null;
      this.cache = new RoomCache(this);
    }

    invalidateCache() {
      this.cache = new RoomCache(this);
    }

    async _ensureCache() {
      if (!this.cache.isInitialized) {
        await this.cache.initialize();
      }
    }

    async get_space_data(since, room_id = null, numRecent = 0) {
      await this._ensureCache();
      const messages = [];
      const roomsToProcess = room_id ? [this.cache.getRoom(room_id)] : Array.from(this.cache.rooms.values());

      for (const room of roomsToProcess) {
        if (!room) continue;
        const roomMessages = room.messages.filter(m => new Date(m.time).getTime() >= since);
        messages.push(...roomMessages);
      }

      messages.sort((a, b) => new Date(a.time) - new Date(b.time));
      const finalMessages = numRecent > 0 ? messages.slice(-numRecent) : messages;

      return {
        messages: finalMessages,
        seren: this.cache.seren
      };
    }

    async get_flagged_spaces() {
      await this._ensureCache();
      return this.cache.getFlaggedRooms();
    }

    async check_message_is_sent(room_id, msg) {
      await this._ensureCache();
      const room = this.cache.getRoom(room_id);
      if (!room) return false;
      return room.messages.some(m => m.content.text?.value?.trim() === msg.content.text?.value?.trim());
    }

    async get_active_rooms(serenName, threshold = 15 * 60 * 1000) {
      await this._ensureCache();
      return this.cache.getActiveRooms(threshold);
    }

    async get_peek_space_info(room_id, serenName) {
      if (!room_id) return null;
      if (serenName) this.cache.seren.name = serenName;
      await this._ensureCache();
      const room = this.cache.rooms.get(room_id);
      if (!room) return null;

      try {
        const account_id = this.cache.seren.account_id;
        const otherMember = room.getOtherMember();
        
        return {
          room_id,
          members: Object.values(room.members),
          events: room.events,
          messages: room.messages,
          seren: { account_id, name: serenName },
          senderName: otherMember?.userName || serenName,
          success: true
        };
      } catch (error) {
        console.error('Error in get_peek_space_info:', error);
        return null;
      }
    }

    async execute_cursor(store, on_success) {
      return new Promise((resolve, reject) => {
        const req = store.openCursor();
        req.onsuccess = () => {
          const cursor = req.result;
          if (cursor) {
            const result = on_success(cursor);
            if (result !== false) resolve(result);
            else cursor.continue();
          } else {
            resolve();
          }
        };
        req.onerror = reject;
      });
    }

    async _withTransaction(storeNames, mode, callback) {
      const db = await this.open_database();
      const tx = db.transaction(storeNames, mode);
      return await callback(tx);
    }

    async open_database() {
      if (this.db) {
        return this.db;
      }

      return new Promise((resolve, reject) => {
        const openRequest = indexedDB.open(this.db_name);
        
        openRequest.onerror = (event) => {
          console.error('Database error:', event.target.error);
          reject(`Error opening database: ${event.target.error?.message || 'Unknown error'}`);
        };
        
        openRequest.onblocked = () => {
          const error = 'Database operation blocked. Please close other instances of this application.';
          console.error(error);
          reject(error);
        };
        
        openRequest.onupgradeneeded = (event) => {
          const db = event.target.result;
          if (!db.objectStoreNames.contains('messages')) {
            db.createObjectStore('messages', { keyPath: 'id' });
          }
        };
        
        openRequest.onsuccess = (event) => {
          this.db = event.target.result;
          this.db.onerror = (event) => {
            console.error('Database error:', event.target.error);
          };
          resolve(this.db);
        };
      });
    }
  }


  window.rc_rooms = {
    RoomsQuery,
    Room,
    RoomCache,
    query: new RoomsQuery()
  };
} else {
  // console.log('rc_rooms already loaded.');
}
