import { logger } from '../shared/logger-client.js';
import { combineMessages } from '../shared/message-combiner.js';
import { sendObservationsToServer } from '../reef.js';
import { DuxConnector } from '../reef/dux-connector.js';

function sleep(ms) { return new Promise(r => setTimeout(r, ms)); }

export class AcceptRequestHandler {
  constructor(seren) {
    this.seren = seren;
    this.tabManager = seren.tabManager;
    this.config = seren.config;
    this.account_id = seren.account_id;
  }

  async checkActiveRooms() {
    const serenName = this.account_id;
    const tabId = this.tabManager.currentChatTabId;
    if (!tabId) {
      logger('AcceptRequestHandler', 'No current chat tab ID found for checking active rooms', 'warn');
      return [];
    }

    try {
      const active_rooms = await chrome.scripting.executeScript({
        target: { tabId },
        func: (serenName) => window.rc_rooms.query.get_active_rooms(serenName),
        args: [serenName]
      });

      const { acceptedInvitations = [] } = await chrome.storage.local.get('acceptedInvitations')
      const tenMinutesAgo = new Date().getTime() - (10 * 60 * 1000)
      const activeRooms = active_rooms[0]?.result || []

      acceptedInvitations.forEach(invite => {
        if (new Date(invite.timestamp).getTime() > tenMinutesAgo) {
          const existingRoom = activeRooms.find(room => room.sender_name === invite.senderName)
          if (!existingRoom) {
            activeRooms.push({
              sender_name: invite.senderName,
              timestamp: invite.timestamp
            })
          }
        }
      })

      return activeRooms;
    } catch (error) {
      logger('AcceptRequestHandler', `Error during checkActiveRooms: ${error.message}`, 'error');
      return [];
    }
  }

  async processRooms(maxRooms = 10) {
    try {
      let activeRooms = [];
      try {
        activeRooms = await this.checkActiveRooms();
      } catch (roomsError) {
        logger('Seren.Rooms', `Error fetching active rooms: ${roomsError.message}`, 'error', { error: roomsError });
        return;
      }

      let lastInviteTime = null;
      try {
        const result = await chrome.storage.local.get('lastInviteTime');
        lastInviteTime = result.lastInviteTime;
      } catch (timeError) {
        logger('Seren.Rooms', `Error retrieving lastInviteTime: ${timeError.message}`, 'error', { error: timeError });
      }

      if (lastInviteTime) {
        const now = Date.now();
        const fiveMinutesAgo = now - (4 * 60 * 1000);
        if (new Date(lastInviteTime).getTime() > fiveMinutesAgo) {
          const remaining = Math.ceil((new Date(lastInviteTime).getTime() + 4 * 60 * 1000 - now) / 1000);
          const minutes = Math.floor(remaining / 60);
          const seconds = remaining % 60;
          logger('Seren.Rooms', `Skipping invite acceptance: cooldown active (${minutes}m ${seconds}s remaining)`, 'info');
          return;
        }
      }

      let blockedRoomIds = [];
      try {
        const blocked = await chrome.storage.local.get(['blockedRoomIds']);
        blockedRoomIds = blocked.blockedRoomIds || [];
      } catch (blockedError) {
        logger('Seren.Rooms', `Error retrieving blockedRooms: ${blockedError.message}`, 'error', { error: blockedError });
      }

      let filteredRooms = activeRooms.filter(room => !blockedRoomIds.includes(room.sender_name));

      try {
        const senderNames = filteredRooms.map(r => r.sender_name);
        if (senderNames.length) {
          const statuses = await DuxConnector.checkFishBulk(
              this.config.duxEndpoint,
              this.config.serenKey,
              senderNames
          );
          const toBlock = statuses.filter(s => s.status >= 1).map(s => s.name);
          if (toBlock.length) {
            const updatedBlocked = Array.from(new Set([...blockedRoomIds, ...toBlock]));
            await chrome.storage.local.set({ blockedRoomIds: updatedBlocked });
            blockedRoomIds = updatedBlocked;
          }
          const allowed = statuses.filter(s => s.status < 1).map(s => s.name);
          filteredRooms = filteredRooms.filter(room => allowed.includes(room.sender_name));
        }
      } catch (statusError) {
        logger('Seren.Rooms', `Error checking room statuses: ${statusError.message}`, 'error', { error: statusError });
      }

      const activeChats = filteredRooms.length;
      const availableSlots = maxRooms - activeChats;
      if (availableSlots <= 0) return;

      const invitesToAccept = availableSlots >= 3
          ? Math.floor(Math.random() * 2) + 2
          : availableSlots;

      logger('Seren.Rooms', `Stats: Open ${filteredRooms.length} Active ${activeRooms.length} `, 'info', { activeRooms, filteredRooms });
      if (invitesToAccept > 0) {
        try {
          logger('Seren.Rooms', `Plan to accept ${invitesToAccept} invites`, 'info', { invitesToAccept });
          await this.acceptInvites({ count: invitesToAccept });
        } catch (acceptError) {
          logger('Seren.Rooms', `Error accepting invites: ${acceptError.message}`, 'error', { error: acceptError });
        }
      }
    } catch (unexpectedError) {
      logger('Seren.Rooms', `Unexpected error: ${unexpectedError.message}`, 'error', { error: unexpectedError });
    }
  }

  async acceptInvites(request) {
    let tabId;
    try {
      tabId = await this.tabManager.currentChatTabId;
      if (!tabId) return { success: false, error: 'No Reddit Chat tab found' };

      const targetCount = request.count || 1;
      let invitesAccepted = 0, failures = 0, lastResult, lastMessage, totalSentCount = 0;
      const serenName = this.account_id;
      const acceptedInvites = [];

      const acquireLock = (await chrome.scripting.executeScript({
        target: { tabId },
        func: () => window.rc_actions?.acquireLock()
      }))[0]?.result

      if (acquireLock) {
        logger('AcceptRequestHandler', 'Starting to accept invites', 'info');
      }
      else{
        logger('AcceptRequestHandler', 'Page is locked for instructions executions, skip this wave','info')
        return
      }

      while (invitesAccepted < targetCount && failures < 1) {
        const roomInfo = (await chrome.scripting.executeScript({
          target: { tabId },
          func: (serenName) => window.rc_requests?.actions?.getFirstRequestRoom(serenName),
          args: [serenName]
        }))[0]?.result
        
        if (!roomInfo.success) {
          return { success: false, error: 'No requests found' };
        }
        
        const senderName = roomInfo.senderName
       
        const fishStatus = await DuxConnector.checkFishStatus(this.config.duxEndpoint,
            this.config.serenKey, senderName)
        const needAccept = fishStatus < 1
        
        if (!needAccept) {
          logger('AcceptRequestHandler', `Skipping invite from ${senderName}`, 'info')
        }
        
        const result = (await chrome.scripting.executeScript({
          target: { tabId },
          func: (roomInfo, needAccept) => window.rc_requests?.actions?.processInvites(roomInfo, needAccept),
          args: [roomInfo, needAccept],
        }))[0]?.result
        
        const isSuccess = result?.success
        if (!isSuccess) {
          logger('AcceptRequestHandler', `Failure: ${result?.error || 'Unknown error'}`, 'warn')
          lastResult = result
          failures++
        } else if (result.roomData) {
          failures = 0
          if (!needAccept) {
            await sleep(1000)
            continue
          }
          invitesAccepted++
          lastResult = result
          
          const existingInvite = acceptedInvites.find(invite => invite.senderName === senderName);
          if (!existingInvite) {
            acceptedInvites.push({
              senderName,
              timestamp: new Date().toISOString()
            });
          }
          
          lastMessage = combineMessages(result.roomData.messages);
          if (lastMessage.length > 0) {
            const extraIds = result.roomData.messages.map(msg => msg.message_id)
            totalSentCount += await sendObservationsToServer(this.config, lastMessage, extraIds)
            logger('AcceptRequestHandler', `Sent ${lastMessage[0].user_name} message: "${lastMessage[0].message_text}"`, 'success', lastMessage)
          } else {
            logger('AcceptRequestHandler', `User did not send any text`, 'warn', lastMessage)
          }
        } else {
          lastResult = result
          logger('AcceptRequestHandler', 'No invites accepted this attempt', 'warn')
          failures++
        }

        await sleep(1500)
      }

      if (failures >= 1 && invitesAccepted < targetCount) {
        logger('AcceptRequestHandler', `Stopped after ${invitesAccepted} invites due to failures`, 'warn')
      }

      if (acceptedInvites.length > 0) {
        const { acceptedInvitations = [] } = await chrome.storage.local.get('acceptedInvitations');
        await chrome.storage.local.set({
          acceptedInvitations: [...acceptedInvitations, ...acceptedInvites],
          lastInviteTime: new Date().toISOString()
        });
        logger('AcceptRequestHandler', `Saved ${acceptedInvites.length} invites to storage`);
      }

      return {
        success: invitesAccepted > 0,
        roomId: lastResult?.roomId,
        error: lastResult?.error,
        lastMessage,
        invitesAccepted,
        sentCount: totalSentCount,
        message: invitesAccepted ? `Successfully accepted ${invitesAccepted} request(s)` : 'No requests were accepted',
        acceptedInvites
      };
    } catch (error) {
      logger('AcceptRequestHandler', `Error in acceptInvites: ${error.message}`, 'error');
      return { success: false, error: error.message };
    } finally {
      if (tabId) {
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            func: () => window.rc_actions?.releaseLock()
          });
        } catch (browserUnlockError) {
          logger('AcceptRequestHandler', `Failed to release browser-side lock: ${browserUnlockError.message}`, 'error');
        }
      }
    }
  }
}
